﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)system.text.json\8.0.0\buildTransitive\net6.0\System.Text.Json.targets" Condition="Exists('$(NuGetPackageRoot)system.text.json\8.0.0\buildTransitive\net6.0\System.Text.Json.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.options\8.0.0\buildTransitive\net6.0\Microsoft.Extensions.Options.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.options\8.0.0\buildTransitive\net6.0\Microsoft.Extensions.Options.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\8.0.0\buildTransitive\net6.0\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\8.0.0\buildTransitive\net6.0\Microsoft.Extensions.Logging.Abstractions.targets')" />
    <Import Project="E:\devexpress\Components\Offline Packages\devexpress.win.navigation\24.2.8\build\net8.0-windows\DevExpress.Win.Navigation.targets" Condition="Exists('E:\devexpress\Components\Offline Packages\devexpress.win.navigation\24.2.8\build\net8.0-windows\DevExpress.Win.Navigation.targets')" />
    <Import Project="E:\devexpress\Components\Offline Packages\devexpress.reporting.core\24.2.8\buildTransitive\DevExpress.Reporting.Core.targets" Condition="Exists('E:\devexpress\Components\Offline Packages\devexpress.reporting.core\24.2.8\buildTransitive\DevExpress.Reporting.Core.targets')" />
  </ImportGroup>
</Project>