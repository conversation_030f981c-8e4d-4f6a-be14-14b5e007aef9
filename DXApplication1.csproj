<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <!-- Database and Entity Framework -->
    <PackageReference Include="DevExpress.Win.Charts" Version="23.2.3" />
    <PackageReference Include="DevExpress.Win.Design" Version="24.2.8" />
    <PackageReference Include="DevExpress.Win.Grid" Version="23.2.3" />
    <PackageReference Include="DevExpress.Win.Navigation" Version="23.2.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0" />

    <!-- Security -->
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />

    <!-- Configuration -->
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />

    <!-- Logging -->
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />

    <!-- PDF Export - Updated to .NET compatible version -->
    <PackageReference Include="iText7" Version="8.0.2" />

    <!-- Chart Controls -->
    <PackageReference Include="System.Windows.Forms.DataVisualization" Version="1.0.0-prerelease.20110.1" />
  </ItemGroup>

</Project>
