{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"DXApplication1/1.0.0": {"dependencies": {"BCrypt.Net-Next": "4.0.3", "DevExpress.Win.Charts": "24.2.8", "DevExpress.Win.Design": "24.2.8", "DevExpress.Win.Grid": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "Microsoft.EntityFrameworkCore.Design": "8.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "8.0.0", "Microsoft.EntityFrameworkCore.Tools": "8.0.0", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Serilog": "3.1.1", "Serilog.Sinks.File": "5.0.0", "System.Windows.Forms.DataVisualization": "1.0.0-prerelease.20110.1", "itext7": "8.0.2"}, "runtime": {"DXApplication1.dll": {}}}, "Azure.Core/1.25.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net5.0/Azure.Core.dll": {"assemblyVersion": "1.25.0.0", "fileVersion": "1.2500.22.33004"}}}, "Azure.Identity/1.7.0": {"dependencies": {"Azure.Core": "1.25.0", "Microsoft.Identity.Client": "4.47.2", "Microsoft.Identity.Client.Extensions.Msal": "2.19.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "8.0.0", "System.Text.Json": "8.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.700.22.46903"}}}, "BCrypt.Net-Next/4.0.3": {"runtime": {"lib/net6.0/BCrypt.Net-Next.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "4.0.3.0"}}}, "DevExpress.Charts/24.2.8": {"dependencies": {"DevExpress.Charts.Core": "24.2.8", "DevExpress.Data": "24.2.8", "DevExpress.DataVisualization.Core": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.XtraCharts.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Charts.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8"}, "runtime": {"lib/net8.0/DevExpress.Charts.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.CodeParser/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "System.CodeDom": "4.4.0"}, "runtime": {"lib/net8.0/DevExpress.CodeParser.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Data/24.2.8": {"dependencies": {"System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.Data.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Data.Desktop/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "System.Data.OleDb": "8.0.1"}, "runtime": {"lib/net8.0-windows/DevExpress.Data.Desktop.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.DataAccess/24.2.8": {"dependencies": {"DevExpress.CodeParser": "24.2.8", "DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Office.Core": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.RichEdit.Core": "24.2.8", "DevExpress.Xpo": "24.2.8", "System.Configuration.ConfigurationManager": "8.0.1", "System.Data.SqlClient": "4.8.6"}, "runtime": {"lib/net8.0/DevExpress.DataAccess.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.DataAccess.UI/24.2.8": {"dependencies": {"DevExpress.CodeParser": "24.2.8", "DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.DataAccess": "24.2.8", "DevExpress.Diagram.Core": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.RichEdit.Core": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Utils.UI": "24.2.8", "DevExpress.Win.Diagram": "24.2.8", "DevExpress.Win.Grid": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "DevExpress.Win.Printing": "24.2.8", "DevExpress.Win.RichEdit": "24.2.8", "DevExpress.Win.TreeList": "24.2.8", "DevExpress.Xpo": "24.2.8", "System.Data.SqlClient": "4.8.6"}, "runtime": {"lib/net8.0-windows/DevExpress.DataAccess.v24.2.UI.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.DataVisualization.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8"}, "runtime": {"lib/net8.0/DevExpress.DataVisualization.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Diagram.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.Diagram.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Drawing/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.Drawing.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Gauges.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.XtraGauges.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Images/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.Images.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Map.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.Map.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Mvvm/24.2.8": {"runtime": {"lib/net8.0-windows/DevExpress.Mvvm.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Office.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Pdf.Core": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.Office.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Pdf.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "System.Drawing.Common": "4.7.2", "System.Security.Cryptography.Pkcs": "8.0.1"}, "runtime": {"lib/net8.0/DevExpress.Pdf.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Pdf.Drawing/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Pdf.Core": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.Pdf.v24.2.Drawing.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.PivotGrid.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "System.Data.OleDb": "8.0.1", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.PivotGrid.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Printing.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Pdf.Core": "24.2.8", "DevExpress.Pdf.Drawing": "24.2.8", "System.Drawing.Common": "4.7.2", "System.Security.Cryptography.Pkcs": "8.0.1", "System.ServiceModel.Http": "6.2.0"}, "runtime": {"lib/net8.0/DevExpress.Printing.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Reporting.Core/24.2.8": {"dependencies": {"DevExpress.Charts": "24.2.8", "DevExpress.Charts.Core": "24.2.8", "DevExpress.CodeParser": "24.2.8", "DevExpress.Data": "24.2.8", "DevExpress.DataAccess": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Gauges.Core": "24.2.8", "DevExpress.Office.Core": "24.2.8", "DevExpress.Pdf.Core": "24.2.8", "DevExpress.Pdf.Drawing": "24.2.8", "DevExpress.PivotGrid.Core": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.RichEdit.Core": "24.2.8", "DevExpress.RichEdit.Export": "24.2.8", "DevExpress.Sparkline.Core": "24.2.8", "DevExpress.Xpo": "24.2.8", "System.CodeDom": "4.4.0", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.XtraReports.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.RichEdit.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Office.Core": "24.2.8", "DevExpress.Pdf.Core": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.RichEdit.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.RichEdit.Export/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Office.Core": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.RichEdit.Core": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.RichEdit.v24.2.Export.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Scheduler.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.XtraScheduler.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Scheduler.CoreDesktop/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Images": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.Scheduler.Core": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraScheduler.v24.2.Core.Desktop.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Sparkline.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.Sparkline.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.SpellChecker.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8"}, "runtime": {"lib/net8.0/DevExpress.SpellChecker.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Spreadsheet.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.DataAccess": "24.2.8", "DevExpress.DataVisualization.Core": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Office.Core": "24.2.8", "DevExpress.Pdf.Core": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.Sparkline.Core": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.Spreadsheet.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.TreeMap/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.DataVisualization.Core": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.TreeMap.Core": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.XtraTreeMap.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.TreeMap.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8"}, "runtime": {"lib/net8.0/DevExpress.TreeMap.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Utils/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.Utils.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Utils.UI/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.DataAccess": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.RichEdit.Core": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "DevExpress.Win.Printing": "24.2.8", "DevExpress.Win.RichEdit": "24.2.8", "DevExpress.Win.TreeList": "24.2.8", "DevExpress.Win.VerticalGrid": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.Utils.v24.2.UI.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.RichEdit.Export": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Win.Grid": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "DevExpress.Win.PivotGrid": "24.2.8", "DevExpress.Win.Printing": "24.2.8", "DevExpress.Win.TreeList": "24.2.8", "DevExpress.Win.VerticalGrid": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraNavBar.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}, "lib/net8.0-windows/DevExpress.XtraWizard.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.Charts/24.2.8": {"dependencies": {"DevExpress.Charts": "24.2.8", "DevExpress.Charts.Core": "24.2.8", "DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.DataAccess.UI": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Utils.UI": "24.2.8", "DevExpress.Win": "24.2.8", "DevExpress.Win.Grid": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "DevExpress.Win.Printing": "24.2.8", "DevExpress.Win.TreeList": "24.2.8", "DevExpress.Win.VerticalGrid": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraCharts.v24.2.Extensions.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}, "lib/net8.0-windows/DevExpress.XtraCharts.v24.2.UI.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}, "lib/net8.0-windows/DevExpress.XtraCharts.v24.2.Wizard.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.Design/24.2.8": {"dependencies": {"DevExpress.Mvvm": "24.2.8", "DevExpress.Win": "24.2.8", "DevExpress.Win.Charts": "24.2.8", "DevExpress.Win.Diagram": "24.2.8", "DevExpress.Win.Dialogs": "24.2.8", "DevExpress.Win.Gantt": "24.2.8", "DevExpress.Win.Gauges": "24.2.8", "DevExpress.Win.Map": "24.2.8", "DevExpress.Win.PdfViewer": "24.2.8", "DevExpress.Win.Reporting": "24.2.8", "DevExpress.Win.RichEdit": "24.2.8", "DevExpress.Win.SchedulerExtensions": "24.2.8", "DevExpress.Win.SpellChecker": "24.2.8", "DevExpress.Win.Spreadsheet": "24.2.8", "DevExpress.Win.TreeMap": "24.2.8", "DevExpress.Xpo": "24.2.8"}}, "DevExpress.Win.Diagram/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Diagram.Core": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Pdf.Core": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "DevExpress.Win.Printing": "24.2.8", "DevExpress.Win.VerticalGrid": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraDiagram.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.Dialogs/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Win.Dialogs.Core": "24.2.8", "DevExpress.Win.Grid": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "DevExpress.Win.TreeList": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraDialogs.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.Dialogs.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.Dialogs.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.Gantt/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "DevExpress.Win.Printing": "24.2.8", "DevExpress.Win.TreeList": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraGantt.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.Gauges/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Gauges.Core": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "DevExpress.Win.Printing": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraGauges.v24.2.Presets.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}, "lib/net8.0-windows/DevExpress.XtraGauges.v24.2.Win.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.Grid/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "DevExpress.Win.Printing": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraGrid.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.Map/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Map.Core": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "DevExpress.Win.Printing": "24.2.8", "System.Data.SqlClient": "4.8.6"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraMap.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.Navigation/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.Sparkline.Core": "24.2.8", "DevExpress.Utils": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraBars.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}, "lib/net8.0-windows/DevExpress.XtraEditors.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}, "lib/net8.0-windows/DevExpress.XtraLayout.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.PdfViewer/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Pdf.Core": "24.2.8", "DevExpress.Pdf.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "DevExpress.Win.TreeList": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraPdfViewer.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.PivotGrid/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.PivotGrid.Core": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Win.Navigation": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraPivotGrid.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.Printing/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.RichEdit.Core": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "DevExpress.Win.TreeList": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraPrinting.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.Reporting/24.2.8": {"dependencies": {"DevExpress.Charts": "24.2.8", "DevExpress.Charts.Core": "24.2.8", "DevExpress.CodeParser": "24.2.8", "DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.DataAccess": "24.2.8", "DevExpress.DataAccess.UI": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Gauges.Core": "24.2.8", "DevExpress.Office.Core": "24.2.8", "DevExpress.PivotGrid.Core": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.Reporting.Core": "24.2.8", "DevExpress.RichEdit.Core": "24.2.8", "DevExpress.Sparkline.Core": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Utils.UI": "24.2.8", "DevExpress.Win": "24.2.8", "DevExpress.Win.Charts": "24.2.8", "DevExpress.Win.Grid": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "DevExpress.Win.PivotGrid": "24.2.8", "DevExpress.Win.Printing": "24.2.8", "DevExpress.Win.RichEdit": "24.2.8", "DevExpress.Win.TreeList": "24.2.8", "DevExpress.Win.VerticalGrid": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraReports.v24.2.Extensions.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.RichEdit/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Images": "24.2.8", "DevExpress.Office.Core": "24.2.8", "DevExpress.Pdf.Core": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.RichEdit.Core": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Win.Grid": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "DevExpress.Win.Printing": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraRichEdit.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.Scheduler/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.Scheduler.Core": "24.2.8", "DevExpress.Scheduler.CoreDesktop": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Win.Grid": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "DevExpress.Win.Printing": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraScheduler.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.SchedulerExtensions/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.Reporting.Core": "24.2.8", "DevExpress.Scheduler.Core": "24.2.8", "DevExpress.Scheduler.CoreDesktop": "24.2.8", "DevExpress.SpellChecker.Core": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "DevExpress.Win.Printing": "24.2.8", "DevExpress.Win.Reporting": "24.2.8", "DevExpress.Win.Scheduler": "24.2.8", "DevExpress.Win.SchedulerReporting": "24.2.8", "DevExpress.Win.SpellChecker": "24.2.8", "DevExpress.Win.TreeList": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraScheduler.v24.2.Extensions.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}, "lib/net8.0-windows/DevExpress.XtraScheduler.v24.2.Reporting.Extensions.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.SchedulerReporting/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.Reporting.Core": "24.2.8", "DevExpress.Scheduler.Core": "24.2.8", "DevExpress.Scheduler.CoreDesktop": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "DevExpress.Win.Printing": "24.2.8", "DevExpress.Win.Scheduler": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraScheduler.v24.2.Reporting.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.SpellChecker/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.SpellChecker.Core": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Win.Navigation": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraSpellChecker.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.Spreadsheet/24.2.8": {"dependencies": {"DevExpress.Charts": "24.2.8", "DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.DataAccess": "24.2.8", "DevExpress.DataAccess.UI": "24.2.8", "DevExpress.DataVisualization.Core": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Images": "24.2.8", "DevExpress.Office.Core": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.RichEdit.Core": "24.2.8", "DevExpress.Spreadsheet.Core": "24.2.8", "DevExpress.TreeMap": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Utils.UI": "24.2.8", "DevExpress.Win": "24.2.8", "DevExpress.Win.Grid": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "DevExpress.Win.Printing": "24.2.8", "DevExpress.Win.RichEdit": "24.2.8", "DevExpress.Win.TreeList": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraSpreadsheet.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.TreeList/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Win.Navigation": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraTreeList.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.TreeMap/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.TreeMap": "24.2.8", "DevExpress.TreeMap.Core": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "DevExpress.Win.Printing": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraTreeMap.v24.2.UI.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Win.VerticalGrid/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Data.Desktop": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.Utils": "24.2.8", "DevExpress.Win.Navigation": "24.2.8", "DevExpress.Win.Printing": "24.2.8"}, "runtime": {"lib/net8.0-windows/DevExpress.XtraVerticalGrid.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Xpo/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "Microsoft.Extensions.DependencyInjection": "8.0.0", "System.Drawing.Common": "4.7.2", "System.Security.Cryptography.Pkcs": "8.0.1", "System.ServiceModel.Http": "6.2.0", "System.ServiceModel.NetTcp": "6.2.0"}, "runtime": {"lib/net8.0/DevExpress.Xpo.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "itext/8.0.2": {"dependencies": {"Microsoft.DotNet.PlatformAbstractions": "1.1.0", "Microsoft.Extensions.DependencyModel": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "System.Collections.NonGeneric": "4.3.0", "System.Diagnostics.Process": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Runtime.Loader": "4.3.0", "System.Runtime.Serialization.Formatters": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Thread": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "System.Xml.XmlDocument": "4.3.0", "itext.commons": "8.0.2"}, "runtime": {"lib/netstandard2.0/itext.barcodes.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.bouncy-castle-connector.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.forms.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.io.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.kernel.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.layout.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.pdfa.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.sign.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.styledxmlparser.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}, "lib/netstandard2.0/itext.svg.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}}}, "itext.commons/8.0.2": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/itext.commons.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}}}, "itext7/8.0.2": {"dependencies": {"itext": "8.0.2"}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {}, "Microsoft.CodeAnalysis.Common/4.5.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.3", "System.Collections.Immutable": "6.0.0", "System.Reflection.Metadata": "6.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.5.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.5.0", "Microsoft.CodeAnalysis.Common": "4.5.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.5.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.CodeAnalysis.Common": "4.5.0", "System.Composition": "6.0.0", "System.IO.Pipelines": "6.0.3", "System.Threading.Channels": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CSharp/4.5.0": {}, "Microsoft.Data.SqlClient/5.1.1": {"dependencies": {"Azure.Identity": "1.7.0", "Microsoft.Data.SqlClient.SNI.runtime": "5.1.0", "Microsoft.Identity.Client": "4.47.2", "Microsoft.IdentityModel.JsonWebTokens": "6.24.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.24.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "8.0.1", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Runtime.Caching": "6.0.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}, "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.0": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.DotNet.PlatformAbstractions/1.1.0": {"dependencies": {"System.AppContext": "4.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Reflection.TypeExtensions": "4.1.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0"}, "runtime": {"lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.0.0"}}}, "Microsoft.EntityFrameworkCore/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.0", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.0": {}, "Microsoft.EntityFrameworkCore.Design/8.0.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.5.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.0", "Microsoft.Extensions.DependencyModel": "8.0.0", "Mono.TextTemplating": "2.2.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Relational/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.SqlServer/8.0.0": {"dependencies": {"Microsoft.Data.SqlClient": "5.1.1", "Microsoft.EntityFrameworkCore.Relational": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Tools/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "8.0.0"}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyModel/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.ObjectPool/6.0.16": {"runtime": {"lib/net6.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1623.17406"}}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Primitives/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Identity.Client/4.47.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.24.0"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.47.2.0", "fileVersion": "4.47.2.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/2.19.3": {"dependencies": {"Microsoft.Identity.Client": "4.47.2", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "2.19.3.0", "fileVersion": "2.19.3.0"}}}, "Microsoft.IdentityModel.Abstractions/6.24.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.24.0", "System.Text.Encoding": "4.3.0", "System.Text.Json": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Logging/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.24.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Protocols/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.24.0", "Microsoft.IdentityModel.Tokens": "6.24.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.24.0", "System.IdentityModel.Tokens.Jwt": "6.24.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Tokens/6.24.0": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.24.0", "System.Security.Cryptography.Cng": "5.0.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.NETCore.Platforms/3.1.4": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.SystemEvents/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4"}}, "Mono.TextTemplating/2.2.1": {"dependencies": {"System.CodeDom": "4.4.0"}, "runtime": {"lib/netstandard2.0/Mono.TextTemplating.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.1.25517"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "Serilog/3.1.1": {"runtime": {"lib/net7.0/Serilog.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "3.1.1.0"}}}, "Serilog.Sinks.File/5.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.AppContext/4.1.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.CodeDom/4.4.0": {}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Composition/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Convention": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0", "System.Composition.TypedParts": "6.0.0"}}, "System.Composition.AttributedModel/6.0.0": {"runtime": {"lib/net6.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Convention/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Hosting/6.0.0": {"dependencies": {"System.Composition.Runtime": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Runtime/6.0.0": {"runtime": {"lib/net6.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.TypedParts/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Configuration.ConfigurationManager/8.0.1": {"dependencies": {"System.Diagnostics.EventLog": "8.0.1", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Data.OleDb/8.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.1", "System.Diagnostics.PerformanceCounter": "8.0.1"}, "runtime": {"lib/net8.0/System.Data.OleDb.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Data.OleDb.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Data.SqlClient/4.8.6": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "5.0.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.DiagnosticSource/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.EventLog/8.0.1": {"runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Diagnostics.PerformanceCounter/8.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.1"}, "runtime": {"lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Diagnostics.Process/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.Win32.Primitives": "4.3.0", "Microsoft.Win32.Registry": "4.7.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Thread": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Drawing.Common/4.7.2": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.Win32.SystemEvents": "4.7.0"}}, "System.Formats.Asn1/5.0.0": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/6.24.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.24.0", "Microsoft.IdentityModel.Tokens": "6.24.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.IO.Pipelines/6.0.3": {"runtime": {"lib/net6.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.522.21309"}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.0"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Metadata/6.0.1": {"dependencies": {"System.Collections.Immutable": "6.0.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Reflection.TypeExtensions/4.1.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Runtime.Caching/6.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.1"}, "runtime": {"lib/net6.0/System.Runtime.Caching.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Serialization.Formatters/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Serialization.Primitives": "4.3.0"}}, "System.Runtime.Serialization.Primitives/4.3.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "5.0.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Pkcs/8.0.1": {"runtime": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/8.0.0": {}, "System.Security.Cryptography.Xml/6.0.1": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Cryptography.Pkcs": "8.0.1"}}, "System.Security.Principal.Windows/5.0.0": {}, "System.ServiceModel.Http/6.2.0": {"dependencies": {"System.ServiceModel.Primitives": "6.2.0"}, "runtime": {"lib/net6.0/System.ServiceModel.Http.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.23.55602"}}, "resources": {"lib/net6.0/cs/System.ServiceModel.Http.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.ServiceModel.Http.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.ServiceModel.Http.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.ServiceModel.Http.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.ServiceModel.Http.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.ServiceModel.Http.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.ServiceModel.Http.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.ServiceModel.Http.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.ServiceModel.Http.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.ServiceModel.Http.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.ServiceModel.Http.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.ServiceModel.Http.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.ServiceModel.Http.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.NetFramingBase/6.2.0": {"dependencies": {"System.ServiceModel.Primitives": "6.2.0"}, "runtime": {"lib/net6.0/System.ServiceModel.NetFramingBase.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.23.55602"}}, "resources": {"lib/net6.0/cs/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.NetTcp/6.2.0": {"dependencies": {"System.ServiceModel.NetFramingBase": "6.2.0", "System.ServiceModel.Primitives": "6.2.0"}, "runtime": {"lib/net6.0/System.ServiceModel.NetTcp.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.23.55602"}}, "resources": {"lib/net6.0/cs/System.ServiceModel.NetTcp.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.ServiceModel.NetTcp.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.ServiceModel.NetTcp.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.ServiceModel.NetTcp.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.ServiceModel.NetTcp.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.ServiceModel.NetTcp.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.ServiceModel.NetTcp.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.ServiceModel.NetTcp.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.ServiceModel.NetTcp.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.ServiceModel.NetTcp.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.ServiceModel.NetTcp.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.ServiceModel.NetTcp.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.ServiceModel.NetTcp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.Primitives/6.2.0": {"dependencies": {"Microsoft.Extensions.ObjectPool": "6.0.16", "System.Security.Cryptography.Xml": "6.0.1"}, "runtime": {"lib/net6.0/System.ServiceModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.23.55602"}}, "resources": {"lib/net6.0/cs/System.ServiceModel.Primitives.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.ServiceModel.Primitives.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.ServiceModel.Primitives.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.ServiceModel.Primitives.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.ServiceModel.Primitives.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.ServiceModel.Primitives.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.ServiceModel.Primitives.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.ServiceModel.Primitives.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.ServiceModel.Primitives.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.ServiceModel.Primitives.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.ServiceModel.Primitives.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.ServiceModel.Primitives.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.ServiceModel.Primitives.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Text.RegularExpressions/4.3.1": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Channels/6.0.0": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Threading.Thread/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading.ThreadPool/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Windows.Forms.DataVisualization/1.0.0-prerelease.20110.1": {"runtime": {"lib/netcoreapp3.0/System.Windows.Forms.DataVisualization.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.20.11001"}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}}}, "libraries": {"DXApplication1/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Azure.Core/1.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-X8Dd4sAggS84KScWIjEbFAdt2U1KDolQopTPoHVubG2y3CM54f9l6asVrP5Uy384NWXjsspPYaJgz5xHc+KvTA==", "path": "azure.core/1.25.0", "hashPath": "azure.core.1.25.0.nupkg.sha512"}, "Azure.Identity/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-eHEiCO/8+MfNc9nH5dVew/+FvxdaGrkRL4OMNwIz0W79+wtJyEoeRlXJ3SrXhoy9XR58geBYKmzMR83VO7bcAw==", "path": "azure.identity/1.7.0", "hashPath": "azure.identity.1.7.0.nupkg.sha512"}, "BCrypt.Net-Next/4.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-W+U9WvmZQgi5cX6FS5GDtDoPzUCV4LkBLkywq/kRZhuDwcbavOzcDAr3LXJFqHUi952Yj3LEYoWW0jbEUQChsA==", "path": "bcrypt.net-next/4.0.3", "hashPath": "bcrypt.net-next.4.0.3.nupkg.sha512"}, "DevExpress.Charts/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-FKxDEiaNzZgtmaLHH0XBGWgx9p3L5RbWcU9ebOqAJqCMk/loZvIuU9832QBvE+5CTaw+RNSeS6bS230dUk+cTw==", "path": "devexpress.charts/24.2.8", "hashPath": "devexpress.charts.24.2.8.nupkg.sha512"}, "DevExpress.Charts.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-afW8PCkGZMApYDk5TdXEApLd6cXVM561fW8Mmx1/F5Q64m+w93WQCYV71WfYBnFnwXBWC0vzvjxBkhfMJ5qrBA==", "path": "devexpress.charts.core/24.2.8", "hashPath": "devexpress.charts.core.24.2.8.nupkg.sha512"}, "DevExpress.CodeParser/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-KN8X1RifFCPaaaazFcA+/ZLuG3c7VmEqNbnkgwcqTh74TBQzCa7heoBII/lIJJI7Ym52Ut3yndiLTSCck5oz+g==", "path": "devexpress.codeparser/24.2.8", "hashPath": "devexpress.codeparser.24.2.8.nupkg.sha512"}, "DevExpress.Data/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-6G7pQcsYF7kTgfWJfYEHtbjtZJSHC7V2+IdcTx+OHWq8Xu51EDE8b2B1j5s+blqBeKXyQcYCQAAexsMHHacAVw==", "path": "devexpress.data/24.2.8", "hashPath": "devexpress.data.24.2.8.nupkg.sha512"}, "DevExpress.Data.Desktop/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-9T5oztR0DZ610CXkF0fCIM56Nbn/wLi926ZGZEFXExZAEU1NQgJbnRO6OHlbRpI51uTAGqTlfUGITFjDw+RSUw==", "path": "devexpress.data.desktop/24.2.8", "hashPath": "devexpress.data.desktop.24.2.8.nupkg.sha512"}, "DevExpress.DataAccess/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-dyA97gfesHQGDJBSL8A8PZFUz1XQkZFmOZoCV4xpMDW0pF9+8ybj05mkeRS3cKIPgTarLALnXHsUD9fRlTZGvg==", "path": "devexpress.dataaccess/24.2.8", "hashPath": "devexpress.dataaccess.24.2.8.nupkg.sha512"}, "DevExpress.DataAccess.UI/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-WK9NjfbtZX2LyPdTmXpAFlYTMGccrmTCc8ya9SdxA+eDRM+A5cCBO4czbkaN7pZYObbSII0cIi1oAtuk9UfJOw==", "path": "devexpress.dataaccess.ui/24.2.8", "hashPath": "devexpress.dataaccess.ui.24.2.8.nupkg.sha512"}, "DevExpress.DataVisualization.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-2hoKvgmdw2Rld9rL0dWPYG2D6OjZVRUoVrDvc1H1JhvRq/F/rei2MYTYFy7GJyIo+5JB0gtmex4L5SN8CRsPCA==", "path": "devexpress.datavisualization.core/24.2.8", "hashPath": "devexpress.datavisualization.core.24.2.8.nupkg.sha512"}, "DevExpress.Diagram.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-QQWVVeG3EWJaZztMPuZpxGkoLkEUXIRyPxbosIqO8tkV4VfmrJYaGz4dHbQVANtWyM8Z+zv1mkL8nRbInB56mQ==", "path": "devexpress.diagram.core/24.2.8", "hashPath": "devexpress.diagram.core.24.2.8.nupkg.sha512"}, "DevExpress.Drawing/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-ALdAEAFWb3Ooyz6dUxZARZaCBlB/HN8sa3ivuW7SZUrbysnJ2nNJ0H0UaTvNIkaZ5K62+8oiomTiHEnqiWOKwA==", "path": "devexpress.drawing/24.2.8", "hashPath": "devexpress.drawing.24.2.8.nupkg.sha512"}, "DevExpress.Gauges.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-CUhJ9zkVUw5ePu5GFHCxJSKrNTPhef0HbGktiD5HXTHG8OVNURFSlPw8yPsFEji3VHuP0KNVT6wQDEbCCruwjg==", "path": "devexpress.gauges.core/24.2.8", "hashPath": "devexpress.gauges.core.24.2.8.nupkg.sha512"}, "DevExpress.Images/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-i3/uFis5Ej0sL+3FjZdQJpgqt16e751K3NTcneXWD1VAbbJCFrcVVxXR50Y3cIvlaeZUjbra/pjyAxkKGut8fA==", "path": "devexpress.images/24.2.8", "hashPath": "devexpress.images.24.2.8.nupkg.sha512"}, "DevExpress.Map.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-sVMdzU1+AFwOcrby1xImocpThYQrl/cKbWm64nWK7Cq1vFHtuPfJcdBERgYD1BZGi5iDRJYLE5C0vPMpkb1aCA==", "path": "devexpress.map.core/24.2.8", "hashPath": "devexpress.map.core.24.2.8.nupkg.sha512"}, "DevExpress.Mvvm/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-GjsfINUDrtyrwoDRq9JZH+0sUz4do51pEeOXp4BHrr5is7c5+Dc1NMjmc8tMGGIjvz7vBjquh/XG2C8J76j79A==", "path": "devexpress.mvvm/24.2.8", "hashPath": "devexpress.mvvm.24.2.8.nupkg.sha512"}, "DevExpress.Office.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-yTl8zP51s2AErC2EiBFzfs6l4wwQ4vEiIZd+qC7FvJMVVTjzTIPgQnHGjOGLT6RUIc6FEREi0U9Ss1yi5PFs2w==", "path": "devexpress.office.core/24.2.8", "hashPath": "devexpress.office.core.24.2.8.nupkg.sha512"}, "DevExpress.Pdf.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-0bslRmvczovySN8kiWMotFl4LoYqcCAfMABEiNGIe/P76XmUnnHZJnjgB+ejZ/MdDLYsK6E/GMV85h35B+R7FA==", "path": "devexpress.pdf.core/24.2.8", "hashPath": "devexpress.pdf.core.24.2.8.nupkg.sha512"}, "DevExpress.Pdf.Drawing/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-FRsvrggRVGWL2JYMC4hgjQzh+D1uBu3GAGzwd8JNwwxWAi4RzKDcYAU+c4EQlXqUwHH7aTfnHac5BVnqsGHkmQ==", "path": "devexpress.pdf.drawing/24.2.8", "hashPath": "devexpress.pdf.drawing.24.2.8.nupkg.sha512"}, "DevExpress.PivotGrid.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-m0ugcNdjISklEqGjPEgNQ8kvnOg1iUNTkTIEesPYpQXywEM3T/Hl1uGzeGaCEOevnVeOzmUZa0ZEGhNhLn6WZA==", "path": "devexpress.pivotgrid.core/24.2.8", "hashPath": "devexpress.pivotgrid.core.24.2.8.nupkg.sha512"}, "DevExpress.Printing.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-j0B/vRWs0O7RugLtfd7cOrosL20CIB5cA1+iyc04mOqoozrnK0XX4u7e38Th1hKl42nPkV+Ijs9HLjAb/MvzdQ==", "path": "devexpress.printing.core/24.2.8", "hashPath": "devexpress.printing.core.24.2.8.nupkg.sha512"}, "DevExpress.Reporting.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-vj6+v34P/CFH4w6dQyNszfAg8PMiW9PzWM/9IeFdoXq1AH76GwXm1uunUjatUUJFrjkMcU3xlyd1ENXglVu5GQ==", "path": "devexpress.reporting.core/24.2.8", "hashPath": "devexpress.reporting.core.24.2.8.nupkg.sha512"}, "DevExpress.RichEdit.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-SMiLKJCxh0pB7ldQKym7H+S2zmTfQupRmyfV0u63+np77BKOO99GYe1INO6/jvhaAu4IHhAd0lrViDUEBzVkPg==", "path": "devexpress.richedit.core/24.2.8", "hashPath": "devexpress.richedit.core.24.2.8.nupkg.sha512"}, "DevExpress.RichEdit.Export/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-Dpo0j65prtoBaZeEaLqrpvRcWwWkhKKLgomUDjWth9D/x+O3HTxQGpkznAx8UTnTf+iDiB74cqiQ18BufX/2cA==", "path": "devexpress.richedit.export/24.2.8", "hashPath": "devexpress.richedit.export.24.2.8.nupkg.sha512"}, "DevExpress.Scheduler.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-Yu//vM52mLd9RB5+NRnzWJthHqOW27fCBcYkfgKz7DYCcsPwTI0DSKwplTkIO+UGQmeKfflWfEd8qdqP4eqZFw==", "path": "devexpress.scheduler.core/24.2.8", "hashPath": "devexpress.scheduler.core.24.2.8.nupkg.sha512"}, "DevExpress.Scheduler.CoreDesktop/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-MAYGqZVLb+OADWItKbDFKdi0UVMfkQu/e2puBPE6FhjaGT1EqvWVbXnP/rooDm/6ngXSNzFl9Au2UvOjLuWyrQ==", "path": "devexpress.scheduler.coredesktop/24.2.8", "hashPath": "devexpress.scheduler.coredesktop.24.2.8.nupkg.sha512"}, "DevExpress.Sparkline.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-4RU+tiVcZYl6p/Ltrq2CH3rObIfZj/wpFKMWW/64Gu7+a2Wn/1z8yB4HM6oYI/N8YeuUb80ebpk2LD0zIf2CsQ==", "path": "devexpress.sparkline.core/24.2.8", "hashPath": "devexpress.sparkline.core.24.2.8.nupkg.sha512"}, "DevExpress.SpellChecker.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-oPTzEiYEWMWr3sVLFGWivuYEPXNcOpQ1FfxkS4LZ9k+MwzeO2w/x9ffc8K6iBmHRgF2RmygvJJHorhHliPtxZw==", "path": "devexpress.spellchecker.core/24.2.8", "hashPath": "devexpress.spellchecker.core.24.2.8.nupkg.sha512"}, "DevExpress.Spreadsheet.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-NbsOnSOqMMfeBAazr+NtqoypcuBc8AoyQP1aEz297MwoqszxZ6dsP+A5a3qPNr/M6QHSnCDqwvEWWOb5jcG4kw==", "path": "devexpress.spreadsheet.core/24.2.8", "hashPath": "devexpress.spreadsheet.core.24.2.8.nupkg.sha512"}, "DevExpress.TreeMap/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-n+dHVpKMrVDW4Fe2ZjjpEd4QQjp12x+OJZObRzoQM91xwVR8mdT+X4hyoJvfZgVXOoZ2EoUOdPBvBFZ2gKAPyA==", "path": "devexpress.treemap/24.2.8", "hashPath": "devexpress.treemap.24.2.8.nupkg.sha512"}, "DevExpress.TreeMap.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-fJc81fepqW7i14vghs9NGfixdyDQgs6BPMlYUfLCa+s6SXJBE+KKUPVOZ0X7korWsdeP56TXd13uV/ar8v64MQ==", "path": "devexpress.treemap.core/24.2.8", "hashPath": "devexpress.treemap.core.24.2.8.nupkg.sha512"}, "DevExpress.Utils/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-j1O8ITRpFR1tDKnXpGpsVHhpbEs+J3/S51FBhCP1Oio1QWw7Fus/vcXU3vacD+fIC+S02un7ka5RXOGemU4+qw==", "path": "devexpress.utils/24.2.8", "hashPath": "devexpress.utils.24.2.8.nupkg.sha512"}, "DevExpress.Utils.UI/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-s0vaS1DBU2tgMmFXL9HL3K42tBpZPhZdinuMctuIOVhI3YJGU+EZb32R4xK71d1ks1WYR71CFiDIhJ13C50Y5w==", "path": "devexpress.utils.ui/24.2.8", "hashPath": "devexpress.utils.ui.24.2.8.nupkg.sha512"}, "DevExpress.Win/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-LJRl0d8izQ6ZN0cTi3kC51O9XGc1IlrklMHVjOizPK/HiFN9C36HY+39fcUKFxoNvyLxQY9spnT0Qfh2xoR4jQ==", "path": "devexpress.win/24.2.8", "hashPath": "devexpress.win.24.2.8.nupkg.sha512"}, "DevExpress.Win.Charts/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-7UzOZ7ikDiojhIKIqo/lfmNUSaTjPZjMVDIJO5PRCKwQWWhNd/oVN/5wR01WL3VSQ1hyCmE/36kEkA77+/9FAA==", "path": "devexpress.win.charts/24.2.8", "hashPath": "devexpress.win.charts.24.2.8.nupkg.sha512"}, "DevExpress.Win.Design/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-tVlrJxlqdkXxA+pJGUXLWVW0qJH6mKx8YKJoj8j//fX3qmvifz58fe7OQUxJnZunO4OuVmrI/sNSgnENBuzpYg==", "path": "devexpress.win.design/24.2.8", "hashPath": "devexpress.win.design.24.2.8.nupkg.sha512"}, "DevExpress.Win.Diagram/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-uRy6CZGuJTjno97D6q+HTckhmA2VCNtP+wqesnUzcnb5vcf5wbtsWEXXHWwCXx8OmNxxUGffy5eyI1XVsssjxA==", "path": "devexpress.win.diagram/24.2.8", "hashPath": "devexpress.win.diagram.24.2.8.nupkg.sha512"}, "DevExpress.Win.Dialogs/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-UBt3wmTEff18OQt+lBsyckxb374L0oLz+k4C+Mvrco1sg5y+O0KxsF5UJQd1QopTpicdbes55PMp2rQ3D4JvLQ==", "path": "devexpress.win.dialogs/24.2.8", "hashPath": "devexpress.win.dialogs.24.2.8.nupkg.sha512"}, "DevExpress.Win.Dialogs.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-en7SWBqWorc7GeTB7D3giYFqycllp+wySq4zkID4CzpMPjvdgf0FDwK6cAs0O4WRR/hDdLc7j4pDKzZ6ghwwCw==", "path": "devexpress.win.dialogs.core/24.2.8", "hashPath": "devexpress.win.dialogs.core.24.2.8.nupkg.sha512"}, "DevExpress.Win.Gantt/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-5Dk5TIR1d7IU/8KPcEgYCF7TI+LRJJVsBSHHmsAZUoX4xzCYeYGK0WtX5xs3I2gZXfSeoM7BAFUItNomEsoqgw==", "path": "devexpress.win.gantt/24.2.8", "hashPath": "devexpress.win.gantt.24.2.8.nupkg.sha512"}, "DevExpress.Win.Gauges/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-u6PqxvuPhR9WNGpCJwiW0kyhGJcf+ZlNIvFybmBQEwvxB5UNMG/zZ4hF/8w6AktT76+9B1wUTl8M8pJ8Y7jpJQ==", "path": "devexpress.win.gauges/24.2.8", "hashPath": "devexpress.win.gauges.24.2.8.nupkg.sha512"}, "DevExpress.Win.Grid/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-E8keIf30nux02r/1cgBQRpA7BMSktvJpihb6PRiVFABGMPnyVnVnk9XpMWMt/ll7AP9AwKUW6DaiO4CbEs4EeA==", "path": "devexpress.win.grid/24.2.8", "hashPath": "devexpress.win.grid.24.2.8.nupkg.sha512"}, "DevExpress.Win.Map/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-q9j/hJvjU7LEY54E91zsd0Wbi2XZ3GdP33jBFLklUiNRgNowjtxNcgJGQByNDu+v1UHDZBiRfoKJI8QxD1WacA==", "path": "devexpress.win.map/24.2.8", "hashPath": "devexpress.win.map.24.2.8.nupkg.sha512"}, "DevExpress.Win.Navigation/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-x9QvrkicgSA8mY7kiAhdCb5H/qlt1Pxmc5FOqN+jGaiD2zVNAtQ4iMAzeyYW8EGiRfd82b/urmPClPm4G0zGrA==", "path": "devexpress.win.navigation/24.2.8", "hashPath": "devexpress.win.navigation.24.2.8.nupkg.sha512"}, "DevExpress.Win.PdfViewer/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-d+XQVz2bRwATo8/b1TQbjygzqIp5F7THigBT8j/YwjXa/dI8zaV7y0QarsGTNQgwlP/Uc88DuI6c7zcJfrgbVQ==", "path": "devexpress.win.pdfviewer/24.2.8", "hashPath": "devexpress.win.pdfviewer.24.2.8.nupkg.sha512"}, "DevExpress.Win.PivotGrid/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-YM2Xj3swVWnj2m/Knb0N0ZZ8YxCzrgApCtkZEbmlMjxc9ysrQU9jRA5hJHdXFDPxzMn/PIsYQVUt81z/yrd0Gw==", "path": "devexpress.win.pivotgrid/24.2.8", "hashPath": "devexpress.win.pivotgrid.24.2.8.nupkg.sha512"}, "DevExpress.Win.Printing/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-iz7eo1LrcjZ/rfbzjHHGopNuLNRXYv2G3+kuEX9prE93PGBVuojdUXK19tqirjYiTRTlmFiS51cgpo0rs7V1aQ==", "path": "devexpress.win.printing/24.2.8", "hashPath": "devexpress.win.printing.24.2.8.nupkg.sha512"}, "DevExpress.Win.Reporting/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-/hGESls0mXqtD17BMEiDvyvmShf09d73xhR7/EVFKof9UnN+f9n1QV407mC/c3kQjUuYkL0YsR61RSHv2GXlfQ==", "path": "devexpress.win.reporting/24.2.8", "hashPath": "devexpress.win.reporting.24.2.8.nupkg.sha512"}, "DevExpress.Win.RichEdit/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-8AIat7U0XC02EkdE42eW8dd8IHkyHSin5SiBd4H1nfEfWOqsp4SbuFITudANY1ApvqtXl7HXwtP7drNrNtosvw==", "path": "devexpress.win.richedit/24.2.8", "hashPath": "devexpress.win.richedit.24.2.8.nupkg.sha512"}, "DevExpress.Win.Scheduler/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-uonwLOUNgYqoVHljq0ViUR3i3OqP5pOajrRe2ecuhiRBInq8vxEf8f9WcHLNcpgf8hkV0kkSFHfltk7Ac8o+pw==", "path": "devexpress.win.scheduler/24.2.8", "hashPath": "devexpress.win.scheduler.24.2.8.nupkg.sha512"}, "DevExpress.Win.SchedulerExtensions/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-1IZIK3mPABQ+cJ0pYkM2GG4kOP8jNfIeL5HudGbXa+IRg+K0is994QRTAypzXC1pG4Re9sYozY3e/oeV4qiJXw==", "path": "devexpress.win.schedulerextensions/24.2.8", "hashPath": "devexpress.win.schedulerextensions.24.2.8.nupkg.sha512"}, "DevExpress.Win.SchedulerReporting/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-cxM7MeXphKz2HCpqKCo4H8lMn4tm0BsHjW9OEUIb8D0h7j8+iFvtkh/z6eNxS96IW5MBryPpLzqgN4OQC29oMg==", "path": "devexpress.win.schedulerreporting/24.2.8", "hashPath": "devexpress.win.schedulerreporting.24.2.8.nupkg.sha512"}, "DevExpress.Win.SpellChecker/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-mP7xkezbDdsVg29YS8t4+m4NV5XI1F0LbfapC8vHvElYlvccAbZslpci6cSqIibt4c/aeiw4nAHN/CHnJi5rVw==", "path": "devexpress.win.spellchecker/24.2.8", "hashPath": "devexpress.win.spellchecker.24.2.8.nupkg.sha512"}, "DevExpress.Win.Spreadsheet/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-0Ik/brJ/YafyQQOOaF8Mu76acAJPUjNLZf5IoLhrhyZgw5MsGgZ0euw4b3D1SGE0sV57lzDx+Um2j9qDyTwdVA==", "path": "devexpress.win.spreadsheet/24.2.8", "hashPath": "devexpress.win.spreadsheet.24.2.8.nupkg.sha512"}, "DevExpress.Win.TreeList/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-2Y1xDj4Hn06Is0clQ9XE3wCsPSkn71L6FmsO6iReQxXi3jW0Dy5JTIyiqO0XJT+52Ostmpn7JHB1jYyNoZ0ENw==", "path": "devexpress.win.treelist/24.2.8", "hashPath": "devexpress.win.treelist.24.2.8.nupkg.sha512"}, "DevExpress.Win.TreeMap/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-eJRCCl5oWhJA07+gQC8rJuumhPstvZy5JKl0kYUh5vhCQclJQlOztEwY/4V1nyRXAZ/UOUMu3grDH+8sGmj1Zw==", "path": "devexpress.win.treemap/24.2.8", "hashPath": "devexpress.win.treemap.24.2.8.nupkg.sha512"}, "DevExpress.Win.VerticalGrid/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-cIbIvhf83nr/mQyMQ4XCOxKNdR7HxKoWF533q422QACdLbplqhSnjOEdjirWM0fF01ezODgozGURPPPG+1guiQ==", "path": "devexpress.win.verticalgrid/24.2.8", "hashPath": "devexpress.win.verticalgrid.24.2.8.nupkg.sha512"}, "DevExpress.Xpo/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-1i16FLh/MRG/CAXFDiR+mLNMux919tbEHN6OW0A6ApF38iU9LEQe3JkVewfGeZda3KNixuH2rmsHX77VIm8cHQ==", "path": "devexpress.xpo/24.2.8", "hashPath": "devexpress.xpo.24.2.8.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "itext/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-79IRTeZZirzRDEtB1PtUMSpsxaJiFz5rAXY4/VtSipsi0qlQpJ/88jxd2SsfX0IggH+wlrsiAc0Y8X2et2T+Bg==", "path": "itext/8.0.2", "hashPath": "itext.8.0.2.nupkg.sha512"}, "itext.commons/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-/F7j7XERA84XgIXDb4j/so1GagAToraVKZIRAdk/ibkvYoU1uwuILU2Ly28sSFf12NN7WIBQHKSk7njJl7Nk8g==", "path": "itext.commons/8.0.2", "hashPath": "itext.commons.8.0.2.nupkg.sha512"}, "itext7/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-IuFfSlMSTLqVGCIsxpcDL9kCtEfppSBjq4V+RgfrxnqEC+iNF4tCHiIdl8quyzTDbaHF4vNufxe2NXMJMkPlSw==", "path": "itext7/8.0.2", "hashPath": "itext7.8.0.2.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-j/rOZtLMVJjrfLRlAMckJLPW/1rze9MT1yfWqSIbUPGRu1m1P0fuo9PmqapwsmePfGB5PJrudQLvmUOAMF0DqQ==", "path": "microsoft.codeanalysis.analyzers/3.3.3", "hashPath": "microsoft.codeanalysis.analyzers.3.3.3.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-lwAbIZNdnY0SUNoDmZHkVUwLO8UyNnyyh1t/4XsbFxi4Ounb3xszIYZaWhyj5ZjyfcwqwmtMbE7fUTVCqQEIdQ==", "path": "microsoft.codeanalysis.common/4.5.0", "hashPath": "microsoft.codeanalysis.common.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-cM59oMKAOxvdv76bdmaKPy5hfj+oR+zxikWoueEB7CwTko7mt9sVKZI8Qxlov0C/LuKEG+WQwifepqL3vuTiBQ==", "path": "microsoft.codeanalysis.csharp/4.5.0", "hashPath": "microsoft.codeanalysis.csharp.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-h74wTpmGOp4yS4hj+EvNzEiPgg/KVs2wmSfTZ81upJZOtPkJsVkgfsgtxxqmAeapjT/vLKfmYV0bS8n5MNVP+g==", "path": "microsoft.codeanalysis.csharp.workspaces/4.5.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4dDRmGELXG72XZaonnOeORyD/T5RpEu5LGHOUIhnv+MmUWDY/m1kWXGwtcgQ5CJ5ynkFiRnIYzTKXYjUs7rbw==", "path": "microsoft.codeanalysis.workspaces.common/4.5.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.5.0.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MW5E9HFvCaV069o8b6YpuRDPBux8s96qDnOJ+4N9QNUCs7c5W3KxwQ+ftpAjbMUlImL+c9WR+l+f5hzjkqhu2g==", "path": "microsoft.data.sqlclient/5.1.1", "hashPath": "microsoft.data.sqlclient.5.1.1.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-jVsElisM5sfBzaaV9kdq2NXZLwIbytetnsOIlJ0cQGgQP4zFNBmkfHBnpwtmKrtBJBEV9+9PVQPVrcCVhDgcIg==", "path": "microsoft.data.sqlclient.sni.runtime/5.1.0", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.1.0.nupkg.sha512"}, "Microsoft.DotNet.PlatformAbstractions/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Bl6KYfbFSIW3QIRHAp931iR5h01qHjKghdpAtncwbzNUs0+IUZ+XfwkIU0sQsR33ufGvi3u4dZMIYYFysjpHAA==", "path": "microsoft.dotnet.platformabstractions/1.1.0", "hashPath": "microsoft.dotnet.platformabstractions.1.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SoODat83pGQUpWB9xULdMX6tuKpq/RTXDuJ2WeC1ldUKcKzLkaFJD1n+I0nOLY58odez/e7z8b6zdp235G/kyg==", "path": "microsoft.entityframeworkcore/8.0.0", "hashPath": "microsoft.entityframeworkcore.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR22s3+zoqlVI7xauFKn1znSIFHO8xuILT+noSwS8bZCKcHz0ydkTDQMuaxSa5WBaQrZmwtTz9rmRvJ7X8mSPQ==", "path": "microsoft.entityframeworkcore.abstractions/8.0.0", "hashPath": "microsoft.entityframeworkcore.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZXxEeLs2zoZ1TA+QoMMcw4f3Tirf8PzgdDax8RoWo0dxI2KmqiEGWYjhm2B/XyWfglc6+mNRyB8rZiQSmxCpeg==", "path": "microsoft.entityframeworkcore.analyzers/8.0.0", "hashPath": "microsoft.entityframeworkcore.analyzers.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-94reKYu63jg4O75UI3LMJHwOSi8tQ6IfubiZhdnSsWcgtmAuF8OyLfjK/MIxuvaQRJZAF6E747FIuxjOtb8/og==", "path": "microsoft.entityframeworkcore.design/8.0.0", "hashPath": "microsoft.entityframeworkcore.design.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fFKkr24cYc7Zw5T6DC4tEyOEPgPbq23BBmym1r9kn4ET9F3HKaetpOeQtV2RryYyUxEeNkJuxgfiZHTisqZc+A==", "path": "microsoft.entityframeworkcore.relational/8.0.0", "hashPath": "microsoft.entityframeworkcore.relational.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GeOmafQn64HyQtYcI/Omv/D/YVHd1zEkWbP3zNQu4oC+usE9K0qOp0R8KgWWFEf8BU4tXuYbok40W0SjfbaK/A==", "path": "microsoft.entityframeworkcore.sqlserver/8.0.0", "hashPath": "microsoft.entityframeworkcore.sqlserver.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zRdaXiiB1gEA0b+AJTd2+drh78gkEA4HyZ1vqNZrKq4xwW8WwavSiQsoeb1UsIMZkocLMBbhQYWClkZzuTKEgQ==", "path": "microsoft.entityframeworkcore.tools/8.0.0", "hashPath": "microsoft.entityframeworkcore.tools.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "path": "microsoft.extensions.caching.memory/8.0.0", "hashPath": "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "path": "microsoft.extensions.configuration.json/8.0.0", "hashPath": "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NSmDw3K0ozNDgShSIpsZcbFIzBX4w28nDag+TfaQujkXGazBm+lid5onlWoCBy4VsLxqnnKjEBbGSJVWJMf43g==", "path": "microsoft.extensions.dependencymodel/8.0.0", "hashPath": "microsoft.extensions.dependencymodel.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/6.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-OVX5tlKg6LY+XKqlUn7i9KY+6Liut0iewWff2DNr7129i/NJ8rpUzbmxavPydZgcLREEWHklXZiPKCS895tNIQ==", "path": "microsoft.extensions.objectpool/6.0.16", "hashPath": "microsoft.extensions.objectpool.6.0.16.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.47.2": {"type": "package", "serviceable": true, "sha512": "sha512-SPgesZRbXoDxg8Vv7k5Ou0ee7uupVw0E8ZCc4GKw25HANRLz1d5OSr0fvTVQRnEswo5Obk8qD4LOapYB+n5kzQ==", "path": "microsoft.identity.client/4.47.2", "hashPath": "microsoft.identity.client.4.47.2.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/2.19.3": {"type": "package", "serviceable": true, "sha512": "sha512-zVVZjn8aW7W79rC1crioDgdOwaFTQorsSO6RgVlDDjc7MvbEGz071wSNrjVhzR0CdQn6Sefx7Abf1o7vasmrLg==", "path": "microsoft.identity.client.extensions.msal/2.19.3", "hashPath": "microsoft.identity.client.extensions.msal.2.19.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-X6aBK56Ot15qKyG7X37KsPnrwah+Ka55NJWPppWVTDi8xWq7CJgeNw2XyaeHgE1o/mW4THwoabZkBbeG2TPBiw==", "path": "microsoft.identitymodel.abstractions/6.24.0", "hashPath": "microsoft.identitymodel.abstractions.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-XDWrkThcxfuWp79AvAtg5f+uRS1BxkIbJnsG/e8VPzOWkYYuDg33emLjp5EWcwXYYIDsHnVZD/00kM/PYFQc/g==", "path": "microsoft.identitymodel.jsonwebtokens/6.24.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-qLYWDOowM/zghmYKXw1yfYKlHOdS41i8t4hVXr9bSI90zHqhyhQh9GwVy8pENzs5wHeytU23DymluC9NtgYv7w==", "path": "microsoft.identitymodel.logging/6.24.0", "hashPath": "microsoft.identitymodel.logging.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-+NzKCkvsQ8X1r/Ff74V7CFr9OsdMRaB6DsV+qpH7NNLdYJ8O4qHbmTnNEsjFcDmk/gVNDwhoL2gN5pkPVq0lwQ==", "path": "microsoft.identitymodel.protocols/6.24.0", "hashPath": "microsoft.identitymodel.protocols.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-a/2RRrc8C9qaw8qdD9hv1ES9YKFgxaqr/SnwMSLbwQZJSUQDd4qx1K4EYgWaQWs73R+VXLyKSxN0f/uE9CsBiQ==", "path": "microsoft.identitymodel.protocols.openidconnect/6.24.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZPqHi86UYuqJXJ7bLnlEctHKkPKT4lGUFbotoCNiXNCSL02emYlcxzGYsRGWWmbFEcYDMi2dcTLLYNzHqWOTsw==", "path": "microsoft.identitymodel.tokens/6.24.0", "hashPath": "microsoft.identitymodel.tokens.6.24.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-9/y05/CuxE+j184Nr4KihhB9KcUkvGojmD4JV4Vt/mHhVZR+eOCD5WCM+CXye9K0OFMsaPXbN+IcaIpjgBGZmg==", "path": "microsoft.netcore.platforms/3.1.4", "hashPath": "microsoft.netcore.platforms.3.1.4.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-mtVirZr++rq+XCDITMUdnETD59XoeMxSpLRIII7JRI6Yj0LEDiO1pPn0ktlnIj12Ix8bfvQqQDMMIF9wC98oCA==", "path": "microsoft.win32.systemevents/4.7.0", "hashPath": "microsoft.win32.systemevents.4.7.0.nupkg.sha512"}, "Mono.TextTemplating/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-KZYeKBET/2Z0gY1WlTAK7+RHTl7GSbtvTLDXEZZojUdAPqpQNDL6tHv7VUpqfX5VEOh+uRGKaZXkuD253nEOBQ==", "path": "mono.texttemplating/2.2.1", "hashPath": "mono.texttemplating.2.2.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HdSSp5MnJSsg08KMfZThpuLPJpPwE5hBXvHwoKWosyHHfe8Mh5WKT0ylEOf6yNzX6Ngjxe4Whkafh5q7Ymac4Q==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+yH1a49wJMy8Zt4yx5RhJrxO/DBDByAiCzNwiETI+1S4mPdCu0OY4djdciC7Vssk0l22wQaDLrXxXkp+3+7bVA==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c3YNH1GQJbfIPJeCnr4avseugSqPrxwIqzthYyZDN6EuOyNOzq+y2KSUfRcXauya1sF4foESTgwM5e1A8arAKw==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NS1U+700m4KFRHR5o4vo9DSlTmlCKu/u7dtE5sUHVIPB+xpXxYQvgBgA6wEIeCz6Yfn0Z52/72WYsToCEPJnrw==", "path": "runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3pthNgxxFcD+Pc0WSEoC0+md3MyhRS6aCEeenvNE3Fdw1HyJ18ZhRFVJJzIeR/O/jpxPboB805Ho0T3Ul7w8A==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KeLz4HClKf+nFS7p/6Fi/CqyLXh81FpiGzcmuS8DGi9lUqSnZ6Es23/gv2O+1XVGfrbNmviF7CckBpavkBoIFQ==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X7IdhILzr4ROXd8mI1BUCQMSHSQwelUlBjF1JyTKCjXaOGn2fB4EKBxQbCK2VjO3WaWIdlXZL3W6TiIVnrhX4g==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nyFNiCk/r+VOiIqreLix8yN+q3Wga9+SE8BCgkf+2BwEKiNx6DyvFjCgkfV743/grxv8jHJ8gUK4XEQw7yzRYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ytoewC6wGorL7KoCAvRfsgoJPJbNq+64k2SqW6JcOAebWsFUvCCYgfzQMrnpvPiEl4OrblUlhF2ji+Q1+SVLrQ==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I8bKw2I8k58Wx7fMKQJn2R8lamboCAiHfHeV/pS65ScKWMMI0+wJkLYlEKvgW1D/XvSl/221clBoR2q9QNNM7A==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VB5cn/7OzUfzdnC8tqAIMQciVLiq2epm2NrAm1E9OjNRyG4lVhfR61SMcLizejzQP8R8Uf/0l5qOIbUEi+RdEg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "Serilog/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-P6G4/4Kt9bT635bhuwdXlJ2SCqqn2nhh4gqFqQueCOr9bK/e7W9ll/IoX1Ter948cV2Z/5+5v8pAfJYUISY03A==", "path": "serilog/3.1.1", "hashPath": "serilog.3.1.1.nupkg.sha512"}, "Serilog.Sinks.File/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "path": "serilog.sinks.file/5.0.0", "hashPath": "serilog.sinks.file.5.0.0.nupkg.sha512"}, "System.AppContext/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-3QjO4jNV7PdKkmQAVp9atA+usVnKRwI3Kx1nMwJ93T0LcQfx7pKAYk0nKz5wn1oP5iqlhZuy6RXOFdhr7rDwow==", "path": "system.appcontext/4.1.0", "hashPath": "system.appcontext.4.1.0.nupkg.sha512"}, "System.CodeDom/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "path": "system.codedom/4.4.0", "hashPath": "system.codedom.4.4.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Composition/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-d7wMuKQtfsxUa7S13tITC8n1cQzewuhD5iDjZtK2prwFfKVzdYtgrTHgjaV03Zq7feGQ5gkP85tJJntXwInsJA==", "path": "system.composition/6.0.0", "hashPath": "system.composition.6.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WK1nSDLByK/4VoC7fkNiFuTVEiperuCN/Hyn+VN30R+W2ijO1d0Z2Qm0ScEl9xkSn1G2MyapJi8xpf4R8WRa/w==", "path": "system.composition.attributedmodel/6.0.0", "hashPath": "system.composition.attributedmodel.6.0.0.nupkg.sha512"}, "System.Composition.Convention/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XYi4lPRdu5bM4JVJ3/UIHAiG6V6lWWUlkhB9ab4IOq0FrRsp0F4wTyV4Dj+Ds+efoXJ3qbLqlvaUozDO7OLeXA==", "path": "system.composition.convention/6.0.0", "hashPath": "system.composition.convention.6.0.0.nupkg.sha512"}, "System.Composition.Hosting/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-w/wXjj7kvxuHPLdzZ0PAUt++qJl03t7lENmb2Oev0n3zbxyNULbWBlnd5J5WUMMv15kg5o+/TCZFb6lSwfaUUQ==", "path": "system.composition.hosting/6.0.0", "hashPath": "system.composition.hosting.6.0.0.nupkg.sha512"}, "System.Composition.Runtime/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qkRH/YBaMPTnzxrS5RDk1juvqed4A6HOD/CwRcDGyPpYps1J27waBddiiq1y93jk2ZZ9wuA/kynM+NO0kb3PKg==", "path": "system.composition.runtime/6.0.0", "hashPath": "system.composition.runtime.6.0.0.nupkg.sha512"}, "System.Composition.TypedParts/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iUR1eHrL8Cwd82neQCJ00MpwNIBs4NZgXzrPqx8NJf/k4+mwBO0XCRmHYJT4OLSwDDqh5nBLJWkz5cROnrGhRA==", "path": "system.composition.typedparts/6.0.0", "hashPath": "system.composition.typedparts.6.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-gPYFPDyohW2gXNhdQRSjtmeS6FymL2crg4Sral1wtvEJ7DUqFCDWDVbbLobASbzxfic8U1hQEdC7hmg9LHncMw==", "path": "system.configuration.configurationmanager/8.0.1", "hashPath": "system.configuration.configurationmanager.8.0.1.nupkg.sha512"}, "System.Data.OleDb/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-RO+/y2ggU5956uQDRXdjA1e2l5yJ4rTWNX76eZ+3sgtYGqGapCe2kQCyiUci+/y6Fyb21Irp4RQEdfrIiuYrxQ==", "path": "system.data.oledb/8.0.1", "hashPath": "system.data.oledb.8.0.1.nupkg.sha512"}, "System.Data.SqlClient/4.8.6": {"type": "package", "serviceable": true, "sha512": "sha512-2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "path": "system.data.sqlclient/4.8.6", "hashPath": "system.data.sqlclient.4.8.6.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-frQDfv0rl209cKm1lnwTgFPzNigy2EKk1BS3uAvHvlBVKe5cymGyHO+Sj+NLv5VF/AhHsqPIUUwya5oV4CHMUw==", "path": "system.diagnostics.diagnosticsource/6.0.0", "hashPath": "system.diagnostics.diagnosticsource.6.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-n1ZP7NM2Gkn/MgD8+eOT5MulMj6wfeQMNS2Pizvq5GHCZfjlFMXV2irQlQmJhwA2VABC57M0auudO89Iu2uRLg==", "path": "system.diagnostics.eventlog/8.0.1", "hashPath": "system.diagnostics.eventlog.8.0.1.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-9RfEDiEjlUADeThs8IPdDVTXSnPRSqjfgTQJALpmGFPKC0k2mbdufOXnb/9JZ4I0TkmxOfy3VTJxrHOJSs8cXg==", "path": "system.diagnostics.performancecounter/8.0.1", "hashPath": "system.diagnostics.performancecounter.8.0.1.nupkg.sha512"}, "System.Diagnostics.Process/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-J0wOX07+QASQblsfxmIMFc9Iq7KTXYL3zs2G/Xc704Ylv3NpuVdo6gij6V3PGiptTxqsK0K7CdXenRvKUnkA2g==", "path": "system.diagnostics.process/4.3.0", "hashPath": "system.diagnostics.process.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Drawing.Common/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-I2y4KBK3VCvU/WqE2xv7NjQ67maXHttkFSHYKgU2evrG9Yqh0oFjfORXt5hZTk+BVjdyFo2h0/YQZsca33BGmg==", "path": "system.drawing.common/4.7.2", "hashPath": "system.drawing.common.4.7.2.nupkg.sha512"}, "System.Formats.Asn1/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MTvUIktmemNB+El0Fgw9egyqT9AYSIk6DTJeoDSpc3GIHxHCMo8COqkWT1mptX5tZ1SlQ6HJZ0OsSvMth1c12w==", "path": "system.formats.asn1/5.0.0", "hashPath": "system.formats.asn1.5.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qibsj9MPWq8S/C0FgvmsLfIlHLE7ay0MJIaAmK94ivN3VyDdglqReed5qMvdQhSL0BzK6v0Z1wB/sD88zVu6Jw==", "path": "system.identitymodel.tokens.jwt/6.24.0", "hashPath": "system.identitymodel.tokens.jwt.6.24.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-ryTgF+iFkpGZY1vRQhfCzX0xTdlV3pyaTTqRu2ETbEv+HlV7O6y7hyQURnghNIXvctl5DuZ//Dpks6HdL/Txgw==", "path": "system.io.pipelines/6.0.3", "hashPath": "system.io.pipelines.6.0.3.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-III/lNMSn0ZRBuM9m5Cgbiho5j81u0FAEagFX5ta2DKbljZ3T0IpD8j+BIiHQPeKqJppWS9bGEp6JnKnWKze0g==", "path": "system.reflection.metadata/6.0.1", "hashPath": "system.reflection.metadata.6.0.1.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-tsQ/ptQ3H5FYfON8lL4MxRk/8kFyE0A+tGPXmVP967cT/gzLHYxIejIYSxp4JmIeFHVP78g/F2FE1mUUTbDtrg==", "path": "system.reflection.typeextensions/4.1.0", "hashPath": "system.reflection.typeextensions.4.1.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Runtime.Caching/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-E0e03kUp5X2k+UAoVl6efmI7uU7JRBWi5EIdlQ7cr0NpBGjHG4fWII35PgsBY9T4fJQ8E4QPsL0rKksU9gcL5A==", "path": "system.runtime.caching/6.0.0", "hashPath": "system.runtime.caching.6.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hWPhJxc453RCa8Z29O91EmfGeZIHX1ZH2A8L6lYQVSaKzku2DfArSfMEb1/MYYzPQRJZeu0c9dmYeJKxW5Fgng==", "path": "system.runtime.interopservices.runtimeinformation/4.0.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.0.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Formatters/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KT591AkTNFOTbhZlaeMVvfax3RqhH1EJlcwF50Wm7sfnBLuHiOeZRRKrr1ns3NESkM20KPZ5Ol/ueMq5vg4QoQ==", "path": "system.runtime.serialization.formatters/4.3.0", "hashPath": "system.runtime.serialization.formatters.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "path": "system.runtime.serialization.primitives/4.3.0", "hashPath": "system.runtime.serialization.primitives.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "path": "system.security.cryptography.pkcs/8.0.1", "hashPath": "system.security.cryptography.pkcs.8.0.1.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg==", "path": "system.security.cryptography.protecteddata/8.0.0", "hashPath": "system.security.cryptography.protecteddata.8.0.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5e5bI28T0x73AwTsbuFP4qSRzthmU2C0Gqgg3AZ3KTxmSyA+Uhk31puA3srdaeWaacVnHhLdJywCzqOiEpbO/w==", "path": "system.security.cryptography.xml/6.0.1", "hashPath": "system.security.cryptography.xml.6.0.1.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.ServiceModel.Http/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-lMk8MEw1OCvyyKY4HMg4ro1eYtWY7azIoDc2FBEGP8uOTJouWn3DemOQvM/GUpgrFbkpjuHPbEG5hgUbNtpiYA==", "path": "system.servicemodel.http/6.2.0", "hashPath": "system.servicemodel.http.6.2.0.nupkg.sha512"}, "System.ServiceModel.NetFramingBase/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-204c9SNDKyQrDKv6F9MLlWKnM7UthRErFByJCHj8y9DtcgMAQnEB5xJvh+9ECmJgG13LJLOAMB5f3CjMatzz/A==", "path": "system.servicemodel.netframingbase/6.2.0", "hashPath": "system.servicemodel.netframingbase.6.2.0.nupkg.sha512"}, "System.ServiceModel.NetTcp/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-FXTDhh8DgCfNyY5k9sNlqvhBVYqVM+0GZBsJfFMH5P5q7qGmTxql3bG9tae1Z+uMXJpG2jLbo1CfgusZ75lADA==", "path": "system.servicemodel.nettcp/6.2.0", "hashPath": "system.servicemodel.nettcp.6.2.0.nupkg.sha512"}, "System.ServiceModel.Primitives/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ro+c4JKNuX6dDpTWh9ZICYr4pIe7uJToauPPgZt2qqFPjVB78ZDUz3rPCZX89dA+IoRZ+9T1ngLBKsgkTmx7UA==", "path": "system.servicemodel.primitives/6.2.0", "hashPath": "system.servicemodel.primitives.6.2.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>drZ<PERSON>2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "path": "system.text.json/8.0.0", "hashPath": "system.text.json.8.0.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-N0kNRrWe4+nXOWlpLT4LAY5brb8caNFlUuIRpraCVMDLYutKkol1aV079rQjLuSxKMJT2SpBQsYX9xbcTMmzwg==", "path": "system.text.regularexpressions/4.3.1", "hashPath": "system.text.regularexpressions.4.3.1.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Channels/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TY8/9+tI0mNaUMgntOxxaq2ndTkdXqLSxvPmas7XEqOlv9lQtB7wLjYGd756lOaO7Dvb5r/WXhluM+0Xe87v5Q==", "path": "system.threading.channels/6.0.0", "hashPath": "system.threading.channels.6.0.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Thread/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "path": "system.threading.thread/4.3.0", "hashPath": "system.threading.thread.4.3.0.nupkg.sha512"}, "System.Threading.ThreadPool/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-k/+g4b7vjdd4aix83sTgC9VG6oXYKAktSfNIJUNGxPEj7ryEOfzHHhfnmsZvjxawwcD9HyWXKCXmPjX8U4zeSw==", "path": "system.threading.threadpool/4.3.0", "hashPath": "system.threading.threadpool.4.3.0.nupkg.sha512"}, "System.Windows.Forms.DataVisualization/1.0.0-prerelease.20110.1": {"type": "package", "serviceable": true, "sha512": "sha512-1EdLFl/I5QobO4shpqUNslDGWTeRbpt3pLMSkiLQYzdWBDqf44/8TM5hXMtkA8ktHU80aVZYVEuxr8vJqcB1Ig==", "path": "system.windows.forms.datavisualization/1.0.0-prerelease.20110.1", "hashPath": "system.windows.forms.datavisualization.1.0.0-prerelease.20110.1.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}}}