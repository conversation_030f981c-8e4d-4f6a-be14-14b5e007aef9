{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\DXApplication1\\DXApplication1\\DXApplication1.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\DXApplication1\\DXApplication1\\DXApplication1.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\DXApplication1\\DXApplication1\\DXApplication1.csproj", "projectName": "DXApplication1", "projectPath": "C:\\Users\\<USER>\\source\\repos\\DXApplication1\\DXApplication1\\DXApplication1.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\DXApplication1\\DXApplication1\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\devexpress\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "E:\\devexpress\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "DevExpress.Win.Charts": {"target": "Package", "version": "[23.2.3, )"}, "DevExpress.Win.Design": {"target": "Package", "version": "[24.2.8, )"}, "DevExpress.Win.Grid": {"target": "Package", "version": "[23.2.3, )"}, "DevExpress.Win.Navigation": {"target": "Package", "version": "[23.2.3, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "System.Windows.Forms.DataVisualization": {"target": "Package", "version": "[1.0.0-prerelease.20110.1, )"}, "iText7": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}