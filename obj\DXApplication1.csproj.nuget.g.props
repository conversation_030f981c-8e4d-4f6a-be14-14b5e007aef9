﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;E:\devexpress\Components\Offline Packages;C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="E:\devexpress\Components\Offline Packages\" />
    <SourceRoot Include="C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.entityframeworkcore\8.0.0\buildTransitive\net8.0\Microsoft.EntityFrameworkCore.props" Condition="Exists('$(NuGetPackageRoot)microsoft.entityframeworkcore\8.0.0\buildTransitive\net8.0\Microsoft.EntityFrameworkCore.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.entityframeworkcore.design\8.0.0\build\net8.0\Microsoft.EntityFrameworkCore.Design.props" Condition="Exists('$(NuGetPackageRoot)microsoft.entityframeworkcore.design\8.0.0\build\net8.0\Microsoft.EntityFrameworkCore.Design.props')" />
    <Import Project="E:\devexpress\Components\Offline Packages\devexpress.data.desktop\24.2.8\build\net8.0-windows\DevExpress.Data.Desktop.props" Condition="Exists('E:\devexpress\Components\Offline Packages\devexpress.data.desktop\24.2.8\build\net8.0-windows\DevExpress.Data.Desktop.props')" />
    <Import Project="E:\devexpress\Components\Offline Packages\devexpress.diagram.core\24.2.8\build\net8.0-windows\DevExpress.Diagram.Core.props" Condition="Exists('E:\devexpress\Components\Offline Packages\devexpress.diagram.core\24.2.8\build\net8.0-windows\DevExpress.Diagram.Core.props')" />
  </ImportGroup>
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgMicrosoft_CodeAnalysis_Analyzers Condition=" '$(PkgMicrosoft_CodeAnalysis_Analyzers)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.3</PkgMicrosoft_CodeAnalysis_Analyzers>
    <PkgMicrosoft_EntityFrameworkCore_Tools Condition=" '$(PkgMicrosoft_EntityFrameworkCore_Tools)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.entityframeworkcore.tools\8.0.0</PkgMicrosoft_EntityFrameworkCore_Tools>
    <PkgDevExpress_Data Condition=" '$(PkgDevExpress_Data)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.data\24.2.8</PkgDevExpress_Data>
    <PkgDevExpress_Xpo Condition=" '$(PkgDevExpress_Xpo)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.xpo\24.2.8</PkgDevExpress_Xpo>
    <PkgDevExpress_Utils Condition=" '$(PkgDevExpress_Utils)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.utils\24.2.8</PkgDevExpress_Utils>
    <PkgDevExpress_Win_Navigation Condition=" '$(PkgDevExpress_Win_Navigation)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.navigation\24.2.8</PkgDevExpress_Win_Navigation>
    <PkgDevExpress_Win_TreeList Condition=" '$(PkgDevExpress_Win_TreeList)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.treelist\24.2.8</PkgDevExpress_Win_TreeList>
    <PkgDevExpress_Win_Printing Condition=" '$(PkgDevExpress_Win_Printing)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.printing\24.2.8</PkgDevExpress_Win_Printing>
    <PkgDevExpress_Win_VerticalGrid Condition=" '$(PkgDevExpress_Win_VerticalGrid)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.verticalgrid\24.2.8</PkgDevExpress_Win_VerticalGrid>
    <PkgDevExpress_Win_TreeMap Condition=" '$(PkgDevExpress_Win_TreeMap)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.treemap\24.2.8</PkgDevExpress_Win_TreeMap>
    <PkgDevExpress_Win_Grid Condition=" '$(PkgDevExpress_Win_Grid)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.grid\24.2.8</PkgDevExpress_Win_Grid>
    <PkgDevExpress_Win_RichEdit Condition=" '$(PkgDevExpress_Win_RichEdit)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.richedit\24.2.8</PkgDevExpress_Win_RichEdit>
    <PkgDevExpress_Win_PivotGrid Condition=" '$(PkgDevExpress_Win_PivotGrid)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.pivotgrid\24.2.8</PkgDevExpress_Win_PivotGrid>
    <PkgDevExpress_Win_Diagram Condition=" '$(PkgDevExpress_Win_Diagram)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.diagram\24.2.8</PkgDevExpress_Win_Diagram>
    <PkgDevExpress_Win Condition=" '$(PkgDevExpress_Win)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win\24.2.8</PkgDevExpress_Win>
    <PkgDevExpress_DataAccess Condition=" '$(PkgDevExpress_DataAccess)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.dataaccess\24.2.8</PkgDevExpress_DataAccess>
    <PkgDevExpress_DataAccess_UI Condition=" '$(PkgDevExpress_DataAccess_UI)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.dataaccess.ui\24.2.8</PkgDevExpress_DataAccess_UI>
    <PkgDevExpress_Win_Spreadsheet Condition=" '$(PkgDevExpress_Win_Spreadsheet)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.spreadsheet\24.2.8</PkgDevExpress_Win_Spreadsheet>
    <PkgDevExpress_Win_SpellChecker Condition=" '$(PkgDevExpress_Win_SpellChecker)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.spellchecker\24.2.8</PkgDevExpress_Win_SpellChecker>
    <PkgDevExpress_Win_Scheduler Condition=" '$(PkgDevExpress_Win_Scheduler)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.scheduler\24.2.8</PkgDevExpress_Win_Scheduler>
    <PkgDevExpress_Win_SchedulerReporting Condition=" '$(PkgDevExpress_Win_SchedulerReporting)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.schedulerreporting\24.2.8</PkgDevExpress_Win_SchedulerReporting>
    <PkgDevExpress_Win_Charts Condition=" '$(PkgDevExpress_Win_Charts)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.charts\24.2.8</PkgDevExpress_Win_Charts>
    <PkgDevExpress_Win_Reporting Condition=" '$(PkgDevExpress_Win_Reporting)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.reporting\24.2.8</PkgDevExpress_Win_Reporting>
    <PkgDevExpress_Win_SchedulerExtensions Condition=" '$(PkgDevExpress_Win_SchedulerExtensions)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.schedulerextensions\24.2.8</PkgDevExpress_Win_SchedulerExtensions>
    <PkgDevExpress_Win_PdfViewer Condition=" '$(PkgDevExpress_Win_PdfViewer)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.pdfviewer\24.2.8</PkgDevExpress_Win_PdfViewer>
    <PkgDevExpress_Win_Map Condition=" '$(PkgDevExpress_Win_Map)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.map\24.2.8</PkgDevExpress_Win_Map>
    <PkgDevExpress_Win_Gauges Condition=" '$(PkgDevExpress_Win_Gauges)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.gauges\24.2.8</PkgDevExpress_Win_Gauges>
    <PkgDevExpress_Win_Gantt Condition=" '$(PkgDevExpress_Win_Gantt)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.gantt\24.2.8</PkgDevExpress_Win_Gantt>
    <PkgDevExpress_Win_Dialogs Condition=" '$(PkgDevExpress_Win_Dialogs)' == '' ">E:\devexpress\Components\Offline Packages\devexpress.win.dialogs\24.2.8</PkgDevExpress_Win_Dialogs>
  </PropertyGroup>
</Project>